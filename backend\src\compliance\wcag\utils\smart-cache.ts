/**
 * Smart Caching System for WCAG Scanning
 * Multi-layer caching for DOM analysis, rule results, and common website patterns
 */

import * as crypto from 'crypto';
import { getPerformanceConfig } from '../../../config/performance';
import logger from '../../../utils/logger';
import fs from 'fs';
import path from 'path';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  hash: string;
  accessCount: number;
  lastAccessed: number;
  size: number; // Estimated size in bytes
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in MB
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default TTL in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableCompression: boolean; // Enable data compression
}

/**
 * Multi-layer smart cache with LRU eviction and intelligent prefetching
 */
export class SmartCache {
  private static instance: SmartCache;
  private domCache = new Map<string, CacheEntry<unknown>>();
  private ruleCache = new Map<string, CacheEntry<unknown>>();
  private patternCache = new Map<string, CacheEntry<unknown>>();
  private siteCache = new Map<string, CacheEntry<unknown>>();

  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
  };

  private config: CacheConfig;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private performanceConfig = getPerformanceConfig();

  protected constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: config?.maxSize || 100, // 100MB default
      maxEntries: config?.maxEntries || this.performanceConfig.maxCacheSize,
      defaultTTL: config?.defaultTTL || this.performanceConfig.cacheExpiryMinutes * 60 * 1000,
      cleanupInterval: config?.cleanupInterval || 300000, // 5 minutes
      enableCompression: config?.enableCompression ?? true,
    };

    this.startCleanupInterval();
    logger.info('🗄️ Smart cache initialized', {
      maxSize: this.config.maxSize,
      maxEntries: this.config.maxEntries,
      defaultTTL: this.config.defaultTTL,
      defaultTTLMinutes: this.config.defaultTTL / 60000,
    });
  }

  static getInstance(config?: Partial<CacheConfig>): SmartCache {
    if (!SmartCache.instance) {
      SmartCache.instance = new SmartCache(config);
    }
    return SmartCache.instance;
  }

  /**
   * Get cached DOM analysis result
   */
  async getDOMAnalysis<T>(url: string, selector: string, contentHash?: string): Promise<T | null> {
    const key = this.generateDOMKey(url, selector, contentHash);
    return this.get<T>(key, 'dom');
  }

  /**
   * Cache DOM analysis result
   */
  async cacheDOMAnalysis<T>(
    url: string,
    selector: string,
    data: T,
    contentHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateDOMKey(url, selector, contentHash);
    await this.set(key, data, 'dom', ttl);
  }

  /**
   * Get cached rule result
   */
  async getRuleResult<T>(
    ruleId: string,
    contentHash: string,
    configHash?: string,
  ): Promise<T | null> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    return this.get<T>(key, 'rule');
  }

  /**
   * Cache rule result
   */
  async cacheRuleResult<T>(
    ruleId: string,
    contentHash: string,
    data: T,
    configHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    await this.set(key, data, 'rule', ttl);
  }

  /**
   * Get cached pattern recognition result
   */
  async getPattern<T>(patternType: string, contentHash: string): Promise<T | null> {
    const key = this.generatePatternKey(patternType, contentHash);
    return this.get<T>(key, 'pattern');
  }

  /**
   * Cache pattern recognition result
   */
  async cachePattern<T>(patternType: string, contentHash: string, data: T): Promise<void> {
    const key = this.generatePatternKey(patternType, contentHash);
    // Patterns have longer TTL as they're more stable
    await this.set(key, data, 'pattern', this.config.defaultTTL * 2);
  }

  /**
   * Get cached site-wide analysis
   */
  async getSiteAnalysis<T>(url: string, analysisType: string): Promise<T | null> {
    const key = this.generateSiteKey(url, analysisType);
    return this.get<T>(key, 'site');
  }

  /**
   * Cache site-wide analysis
   */
  async cacheSiteAnalysis<T>(
    url: string,
    analysisType: string,
    data: T,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateSiteKey(url, analysisType);
    await this.set(key, data, 'site', ttl);
  }

  /**
   * Generic get method
   */
  async get<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
  ): Promise<T | null> {
    const cache = this.getCache(cacheType);
    let entry = cache.get(key);

    // If not in memory, try file-based cache
    if (!entry) {
      const fileEntry = await this.loadFromFile(key);
      if (fileEntry) {
        // Restore to memory cache
        entry = fileEntry;
        cache.set(key, entry);
        logger.debug(`📁 Restored from file cache: ${cacheType}:${key.substring(0, 50)}...`);
      }
    }

    if (!entry) {
      this.stats.misses++;
      logger.debug(`🔍 Cache miss: ${cacheType}:${key.substring(0, 50)}...`);
      logger.debug(`📊 Cache stats: ${this.stats.hits} hits, ${this.stats.misses} misses, ${cache.size} entries`);
      return null;
    }

    // Check if entry has expired
    const age = Date.now() - entry.timestamp;
    if (age > this.config.defaultTTL) {
      cache.delete(key);
      this.stats.misses++;
      logger.debug(`⏰ Cache expired: ${cacheType}:${key.substring(0, 50)}... (age: ${Math.round(age / 1000)}s, TTL: ${Math.round(this.config.defaultTTL / 1000)}s)`);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    logger.debug(`✅ Cache hit: ${cacheType}:${key.substring(0, 50)}... (accessed ${entry.accessCount} times, age: ${Math.round(age / 1000)}s)`);
    return entry.data as T;
  }

  /**
   * Generic set method
   */
  async set<T>(
    key: string,
    data: T,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    _ttl?: number,
  ): Promise<void> {
    const cache = this.getCache(cacheType);
    const now = Date.now();
    const dataSize = this.estimateSize(data);

    // Check if we need to evict entries
    await this.ensureCapacity(cache, dataSize);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      hash: this.generateHash(data),
      accessCount: 1,
      lastAccessed: now,
      size: dataSize,
    };

    cache.set(key, entry);

    // Also save to file-based cache for persistence
    await this.saveToFile(key, entry);

    logger.debug(`💾 Cached: ${cacheType}:${key} (${dataSize} bytes)`);
  }

  /**
   * Ensure cache has capacity for new entry
   */
  private async ensureCapacity(
    cache: Map<string, CacheEntry<unknown>>,
    newEntrySize: number,
  ): Promise<void> {
    const currentSize = this.calculateCacheSize(cache);
    const maxSizeBytes = this.config.maxSize * 1024 * 1024; // Convert MB to bytes

    // Check size limit
    if (currentSize + newEntrySize > maxSizeBytes) {
      await this.evictLRU(cache, newEntrySize);
    }

    // Check entry count limit
    if (cache.size >= this.config.maxEntries) {
      await this.evictLRU(cache, 0);
    }
  }

  /**
   * Evict least recently used entries
   */
  private async evictLRU(
    cache: Map<string, CacheEntry<unknown>>,
    requiredSpace: number,
  ): Promise<void> {
    const entries = Array.from(cache.entries());

    // Sort by last accessed time (oldest first)
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    let freedSpace = 0;
    let evictedCount = 0;

    for (const [key, entry] of entries) {
      cache.delete(key);
      freedSpace += entry.size;
      evictedCount++;
      this.stats.evictions++;

      // Stop if we've freed enough space and are under entry limit
      if (freedSpace >= requiredSpace && cache.size < this.config.maxEntries) {
        break;
      }
    }

    logger.debug(`🗑️ Evicted ${evictedCount} entries, freed ${freedSpace} bytes`);
  }

  /**
   * Calculate total cache size in bytes
   */
  private calculateCacheSize(cache: Map<string, CacheEntry<unknown>>): number {
    let totalSize = 0;
    for (const entry of Array.from(cache.values())) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Estimate object size in bytes
   */
  protected estimateSize(obj: unknown): number {
    const jsonString = JSON.stringify(obj);
    return Buffer.byteLength(jsonString, 'utf8');
  }

  /**
   * Get cache instance by type
   */
  private getCache(type: 'dom' | 'rule' | 'pattern' | 'site'): Map<string, CacheEntry<unknown>> {
    switch (type) {
      case 'dom':
        return this.domCache;
      case 'rule':
        return this.ruleCache;
      case 'pattern':
        return this.patternCache;
      case 'site':
        return this.siteCache;
      default:
        throw new Error(`Unknown cache type: ${type}`);
    }
  }

  /**
   * Generate cache keys
   */
  protected generateDOMKey(url: string, selector: string, contentHash?: string): string {
    const baseKey = `${this.normalizeUrl(url)}:${selector}`;
    return contentHash ? `${baseKey}:${contentHash}` : baseKey;
  }

  protected generateRuleKey(ruleId: string, contentHash: string, configHash?: string): string {
    const baseKey = `${ruleId}:${contentHash}`;
    return configHash ? `${baseKey}:${configHash}` : baseKey;
  }

  protected generatePatternKey(patternType: string, contentHash: string): string {
    return `${patternType}:${contentHash}`;
  }

  protected generateSiteKey(url: string, analysisType: string): string {
    return `${this.normalizeUrl(url)}:${analysisType}`;
  }

  /**
   * Normalize URL for consistent caching
   */
  protected normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      // Remove query parameters and fragments for more cache hits
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }

  /**
   * Generate hash for data integrity
   */
  private generateHash(data: unknown): string {
    return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
  }

  /**
   * Generate optimized content hash for better cache hits
   * Focuses on structural elements rather than dynamic content
   */
  generateOptimizedContentHash(pageContent: string): string {
    try {
      // Extract only structural elements for more stable hashing
      const structuralContent = pageContent
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
        .replace(/<!--[\s\S]*?-->/g, '') // Remove comments
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/data-\w+="[^"]*"/g, '') // Remove data attributes
        .replace(/id="[^"]*"/g, '') // Remove IDs (often dynamic)
        .replace(/class="[^"]*"/g, '') // Remove classes (focus on structure)
        .replace(/timestamp[^>]*>/gi, '') // Remove timestamps
        .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g, '') // Remove ISO timestamps
        .replace(/\d{13}/g, '') // Remove 13-digit timestamps
        .replace(/\d{10}/g, '') // Remove 10-digit timestamps
        .replace(/nonce="[^"]*"/gi, '') // Remove nonces
        .replace(/csrf[^>]*>/gi, '') // Remove CSRF tokens
        .trim();

      return crypto.createHash('md5').update(structuralContent).digest('hex');
    } catch (error) {
      logger.warn('Failed to generate optimized content hash, using fallback', { error });
      return crypto.createHash('md5').update(pageContent).digest('hex');
    }
  }

  /**
   * Generate stable cache key that excludes dynamic content
   */
  generateStableCacheKey(ruleId: string, url: string, options: Record<string, unknown> = {}): string {
    try {
      // Create stable URL hash (remove query params that change)
      const urlObj = new URL(url);
      const stableUrl = `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;

      // Create stable options hash (exclude dynamic fields)
      const stableOptions = {
        wcagVersion: options.wcagVersion || '2.2',
        level: options.level || 'AA',
        // Exclude dynamic fields like scanId, timestamp, etc.
      };

      const optionsHash = crypto.createHash('md5')
        .update(JSON.stringify(stableOptions))
        .digest('hex')
        .substring(0, 8);

      const urlHash = crypto.createHash('md5')
        .update(stableUrl)
        .digest('hex')
        .substring(0, 8);

      return `rule:${ruleId}:${urlHash}:${optionsHash}`;
    } catch (error) {
      logger.warn('Failed to generate stable cache key, using fallback', { error });
      return `rule:${ruleId}:${Date.now()}`;
    }
  }

  /**
   * Normalize cache key for consistent file naming
   */
  private normalizeKeyForFile(key: string): string {
    try {
      // Remove or replace characters that are problematic for filenames
      let normalized = key
        .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid filename characters
        .replace(/\s+/g, '_') // Replace spaces with underscores
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .toLowerCase(); // Consistent case

      // Limit length to prevent filesystem issues
      if (normalized.length > 200) {
        const hash = crypto.createHash('md5').update(key).digest('hex');
        normalized = normalized.substring(0, 150) + '_' + hash;
      }

      return normalized;
    } catch (error) {
      // Fallback to hash if normalization fails
      return crypto.createHash('md5').update(key).digest('hex');
    }
  }

  /**
   * File-based cache fallback for persistence
   */
  private getCacheDir(): string {
    const cacheDir = path.join(process.cwd(), 'cache', 'wcag');
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }
    return cacheDir;
  }

  /**
   * Save cache entry to file system with enhanced validation
   */
  private async saveToFile(key: string, entry: CacheEntry<any>): Promise<void> {
    try {
      const cacheDir = this.getCacheDir();

      // Generate consistent filename with validation
      const normalizedKey = this.normalizeKeyForFile(key);
      const fileName = crypto.createHash('md5').update(normalizedKey).digest('hex') + '.json';
      const filePath = path.join(cacheDir, fileName);

      // Add metadata for debugging and validation
      const fileData = {
        ...entry,
        metadata: {
          originalKey: key,
          normalizedKey,
          savedAt: Date.now(),
          version: '1.1',
          keyHash: crypto.createHash('md5').update(key).digest('hex')
        }
      };

      await fs.promises.writeFile(filePath, JSON.stringify(fileData, null, 2), 'utf8');
      logger.debug(`💾 Cache saved to file: ${fileName} (key: ${key.substring(0, 30)}...)`);
    } catch (error) {
      logger.warn('Failed to save cache entry to file', { key: key.substring(0, 50), error });
    }
  }

  /**
   * Load cache entry from file system with enhanced validation
   */
  private async loadFromFile(key: string): Promise<CacheEntry<any> | null> {
    try {
      const cacheDir = this.getCacheDir();

      // Use same normalization as save
      const normalizedKey = this.normalizeKeyForFile(key);
      const fileName = crypto.createHash('md5').update(normalizedKey).digest('hex') + '.json';
      const filePath = path.join(cacheDir, fileName);

      if (!fs.existsSync(filePath)) {
        logger.debug(`📁 Cache file not found: ${fileName} (key: ${key.substring(0, 30)}...)`);
        return null;
      }

      const content = await fs.promises.readFile(filePath, 'utf8');
      const fileData = JSON.parse(content);

      // Validate file data structure
      if (!fileData || !fileData.data || !fileData.timestamp) {
        logger.warn(`📁 Invalid cache file structure: ${fileName}`);
        await fs.promises.unlink(filePath).catch(() => {});
        return null;
      }

      // Check if entry is expired
      const config = getPerformanceConfig();
      const maxAge = config.cacheExpiryMinutes * 60 * 1000;
      const age = Date.now() - fileData.timestamp;

      if (age > maxAge) {
        logger.debug(`📁 Cache file expired: ${fileName} (age: ${Math.round(age / 1000)}s)`);
        await fs.promises.unlink(filePath).catch(() => {}); // Clean up expired file
        return null;
      }

      // Validate key consistency (optional debugging)
      if (fileData.metadata?.originalKey && fileData.metadata.originalKey !== key) {
        logger.debug(`📁 Key mismatch in cache file: ${fileName}`);
      }

      logger.debug(`📁 Cache loaded from file: ${fileName} (key: ${key.substring(0, 30)}...)`);
      return fileData;
    } catch (error) {
      logger.warn('Failed to load cache entry from file', {
        key: key.substring(0, 50),
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Perform cache cleanup
   */
  private performCleanup(): void {
    const now = Date.now();
    let totalCleaned = 0;

    const caches = [
      { name: 'dom', cache: this.domCache },
      { name: 'rule', cache: this.ruleCache },
      { name: 'pattern', cache: this.patternCache },
      { name: 'site', cache: this.siteCache },
    ];

    for (const { name, cache } of caches) {
      let cleaned = 0;
      for (const [key, entry] of Array.from(cache.entries())) {
        if (now - entry.timestamp > this.config.defaultTTL) {
          cache.delete(key);
          cleaned++;
        }
      }
      totalCleaned += cleaned;
      if (cleaned > 0) {
        logger.debug(`🧹 Cleaned ${cleaned} expired entries from ${name} cache`);
      }
    }

    if (totalCleaned > 0) {
      logger.info(`🧹 Cache cleanup completed: ${totalCleaned} entries removed`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalEntries =
      this.domCache.size + this.ruleCache.size + this.patternCache.size + this.siteCache.size;

    const totalSize =
      this.calculateCacheSize(this.domCache) +
      this.calculateCacheSize(this.ruleCache) +
      this.calculateCacheSize(this.patternCache) +
      this.calculateCacheSize(this.siteCache);

    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    const missRate = totalRequests > 0 ? (this.stats.misses / totalRequests) * 100 : 0;

    // Find oldest and newest entries
    let oldestEntry = Date.now();
    let newestEntry = 0;

    const allCaches = [this.domCache, this.ruleCache, this.patternCache, this.siteCache];
    for (const cache of allCaches) {
      for (const entry of Array.from(cache.values())) {
        oldestEntry = Math.min(oldestEntry, entry.timestamp);
        newestEntry = Math.max(newestEntry, entry.timestamp);
      }
    }

    return {
      totalEntries,
      totalSize,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      evictionCount: this.stats.evictions,
      oldestEntry: oldestEntry === Date.now() ? 0 : oldestEntry,
      newestEntry,
    };
  }

  /**
   * Clear specific cache type
   */
  clearCache(type: 'dom' | 'rule' | 'pattern' | 'site' | 'all'): void {
    if (type === 'all') {
      this.domCache.clear();
      this.ruleCache.clear();
      this.patternCache.clear();
      this.siteCache.clear();
      logger.info('🗑️ All caches cleared');
    } else {
      this.getCache(type).clear();
      logger.info(`🗑️ ${type} cache cleared`);
    }
  }

  /**
   * Warm up cache with common patterns
   */
  async warmupCache(commonUrls: string[]): Promise<void> {
    logger.info(`🔥 Warming up cache with ${commonUrls.length} URLs`);

    // Pre-populate cache with common DOM patterns
    const commonPatterns = [
      'button', 'input', 'a', 'img', 'form', 'nav', 'main', 'header', 'footer',
      '[role="button"]', '[role="link"]', '[aria-label]', '[alt]', 'h1,h2,h3,h4,h5,h6'
    ];

    for (const url of commonUrls) {
      const normalizedUrl = this.normalizeUrl(url);

      // Cache common DOM patterns for this URL
      for (const pattern of commonPatterns) {
        const key = this.generateDOMKey(normalizedUrl, pattern);
        // Pre-cache empty results to avoid repeated DOM queries
        await this.set(key, { elements: [], count: 0 }, 'dom', this.config.defaultTTL);
      }

      // Cache common site-wide analysis patterns
      const siteAnalysisTypes = ['framework', 'cms', 'ecommerce', 'media'];
      for (const analysisType of siteAnalysisTypes) {
        const key = this.generateSiteKey(normalizedUrl, analysisType);
        await this.set(key, { detected: false, confidence: 0 }, 'site', this.config.defaultTTL * 2);
      }

      logger.debug(`🔥 Warmed up cache for: ${normalizedUrl}`);
    }

    logger.info(`🔥 Cache warming completed for ${commonUrls.length} URLs`);
  }

  /**
   * Shutdown cache
   */
  shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.clearCache('all');
    this.stats = { hits: 0, misses: 0, evictions: 0 };

    logger.info('🔄 Smart cache shutdown completed');
  }
}

export default SmartCache;
