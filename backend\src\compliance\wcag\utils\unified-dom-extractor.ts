/**
 * Unified DOM Extraction Utility
 * Extracts complete page structure once and supplies to all 66 WCAG checks
 * Reduces redundant DOM parsing and improves performance significantly
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import SmartCache from './smart-cache';

export interface FormElement {
  selector: string;
  tagName: string;
  action: string;
  method: string;
  hasLabel: boolean;
  fields: InputElement[];
  accessibility: {
    hasAccessibleName: boolean;
    hasErrorHandling: boolean;
    hasValidation: boolean;
  };
}

export interface ImageElement {
  selector: string;
  src: string;
  alt: string;
  hasAltText: boolean;
  isDecorative: boolean;
  dimensions: { width: number; height: number };
  accessibility: {
    hasProperAlt: boolean;
    hasLongDesc: boolean;
    hasAriaLabel: boolean;
  };
}

export interface LinkElement {
  selector: string;
  href: string;
  text: string;
  hasText: boolean;
  isExternal: boolean;
  accessibility: {
    hasAccessibleName: boolean;
    hasProperContext: boolean;
    isKeyboardAccessible: boolean;
  };
}

export interface HeadingElement {
  selector: string;
  tagName: string;
  level: number;
  text: string;
  hasText: boolean;
  accessibility: {
    hasProperHierarchy: boolean;
    isUnique: boolean;
  };
}

export interface InputElement {
  selector: string;
  type: string;
  name: string;
  id: string;
  hasLabel: boolean;
  labelText: string;
  isRequired: boolean;
  accessibility: {
    hasAccessibleName: boolean;
    hasDescription: boolean;
    hasValidation: boolean;
    isKeyboardAccessible: boolean;
  };
}

export interface ButtonElement {
  selector: string;
  type: string;
  text: string;
  hasText: boolean;
  accessibility: {
    hasAccessibleName: boolean;
    isKeyboardAccessible: boolean;
    hasProperRole: boolean;
  };
}

export interface MultimediaElement {
  selector: string;
  type: 'video' | 'audio' | 'iframe';
  src: string;
  accessibility: {
    hasCaptions: boolean;
    hasTranscript: boolean;
    hasAudioDescription: boolean;
    hasAccessibleControls: boolean;
  };
}

export interface LandmarkElement {
  selector: string;
  role: string;
  hasLabel: boolean;
  labelText: string;
}

export interface AriaElement {
  selector: string;
  ariaAttributes: Record<string, string>;
  hasValidAria: boolean;
}

export interface FocusableElement {
  selector: string;
  tabIndex: number;
  isKeyboardAccessible: boolean;
  hasFocusIndicator: boolean;
}

export interface SemanticElement {
  selector: string;
  tagName: string;
  semanticMeaning: string;
  hasProperStructure: boolean;
}

export interface PageStructure {
  html: string;
  metadata: {
    title: string;
    description: string;
    keywords: string[];
    lang: string;
    charset: string;
    viewport: string;
  };
  elements: {
    forms: FormElement[];
    images: ImageElement[];
    links: LinkElement[];
    headings: HeadingElement[];
    inputs: InputElement[];
    buttons: ButtonElement[];
    multimedia: MultimediaElement[];
  };
  accessibility: {
    landmarks: LandmarkElement[];
    ariaElements: AriaElement[];
    focusableElements: FocusableElement[];
  };
  structure: {
    doctype: string;
    hasValidHTML: boolean;
    semanticStructure: SemanticElement[];
    colorContrast: {
      textElements: Array<{
        selector: string;
        foreground: string;
        background: string;
        ratio: number;
      }>;
    };
  };
  performance: {
    extractionTime: number;
    elementCounts: Record<string, number>;
    cacheKey: string;
  };
}

/**
 * Unified DOM Extractor - Singleton pattern for efficient reuse
 */
export class UnifiedDOMExtractor {
  private static instance: UnifiedDOMExtractor;
  private cache: Map<string, PageStructure> = new Map();
  private smartCache: SmartCache;

  private constructor() {
    this.smartCache = SmartCache.getInstance();
  }

  static getInstance(): UnifiedDOMExtractor {
    if (!UnifiedDOMExtractor.instance) {
      UnifiedDOMExtractor.instance = new UnifiedDOMExtractor();
    }
    return UnifiedDOMExtractor.instance;
  }

  /**
   * Extract complete page structure for all WCAG checks
   */
  async extractPageStructure(page: Page, url: string): Promise<PageStructure> {
    const cacheKey = this.generateCacheKey(url);
    
    // Check memory cache first
    if (this.cache.has(cacheKey)) {
      logger.debug('📋 Using cached page structure (memory)');
      return this.cache.get(cacheKey)!;
    }

    // Check persistent cache
    const cachedStructure = await this.smartCache.get<PageStructure>(cacheKey, 'dom');
    if (cachedStructure) {
      logger.debug('📋 Using cached page structure (persistent)');
      this.cache.set(cacheKey, cachedStructure);
      return cachedStructure;
    }

    const startTime = Date.now();
    logger.info('📋 Extracting page structure for all WCAG checks...');

    try {
      // Inject extraction functions into the page
      await this.injectExtractionFunctions(page);

      // Extract all data in a single page evaluation
      const extractedData = await page.evaluate(() => {
        const extractor = (window as any).unifiedExtractor;
        return {
          html: document.documentElement.outerHTML,
          metadata: extractor.extractMetadata(),
          elements: extractor.extractElements(),
          accessibility: extractor.extractAccessibilityInfo(),
          structure: extractor.extractStructuralInfo(),
        };
      });

      const extractionTime = Date.now() - startTime;

      const pageStructure: PageStructure = {
        ...extractedData,
        performance: {
          extractionTime,
          elementCounts: this.calculateElementCounts(extractedData),
          cacheKey,
        },
      };

      // Cache the result
      this.cache.set(cacheKey, pageStructure);
      await this.smartCache.set(cacheKey, pageStructure, 'dom');

      logger.info(`📋 Page structure extracted in ${extractionTime}ms - ${Object.keys(pageStructure.elements).reduce((sum, key) => sum + pageStructure.elements[key as keyof typeof pageStructure.elements].length, 0)} elements found`);
      
      return pageStructure;
    } catch (error) {
      logger.error('❌ Failed to extract page structure:', error);
      throw new Error(`Page structure extraction failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate cache key for page structure
   */
  private generateCacheKey(url: string): string {
    try {
      const urlObj = new URL(url);
      // Remove query parameters for better cache hits
      const baseUrl = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
      const timestamp = Math.floor(Date.now() / (1000 * 60 * 60)); // Hour-based versioning
      return `dom-structure:${baseUrl}:${timestamp}`;
    } catch {
      return `dom-structure:${url}:${Date.now()}`;
    }
  }

  /**
   * Calculate element counts for performance metrics
   */
  private calculateElementCounts(data: any): Record<string, number> {
    return {
      forms: data.elements?.forms?.length || 0,
      images: data.elements?.images?.length || 0,
      links: data.elements?.links?.length || 0,
      headings: data.elements?.headings?.length || 0,
      inputs: data.elements?.inputs?.length || 0,
      buttons: data.elements?.buttons?.length || 0,
      multimedia: data.elements?.multimedia?.length || 0,
      landmarks: data.accessibility?.landmarks?.length || 0,
      ariaElements: data.accessibility?.ariaElements?.length || 0,
      focusableElements: data.accessibility?.focusableElements?.length || 0,
    };
  }

  /**
   * Clear cache to free memory
   */
  clearCache(): void {
    this.cache.clear();
    logger.debug('📋 DOM extraction cache cleared');
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }

  /**
   * Inject extraction functions into the page context
   */
  private async injectExtractionFunctions(page: Page): Promise<void> {
    try {
      await page.evaluateOnNewDocument(() => {
        if (typeof window === 'undefined') {
          console.error('❌ Window object not available for DOM extraction');
          return;
        }

        (window as any).unifiedExtractor = {
          /**
           * Extract page metadata
           */
          extractMetadata() {
            const title = document.title || '';
            const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
            const keywords = document.querySelector('meta[name="keywords"]')?.getAttribute('content')?.split(',').map(k => k.trim()) || [];
            const lang = document.documentElement.lang || '';
            const charset = document.characterSet || '';
            const viewport = document.querySelector('meta[name="viewport"]')?.getAttribute('content') || '';

            return { title, description, keywords, lang, charset, viewport };
          },

          /**
           * Extract all page elements
           */
          extractElements() {
            return {
              forms: this.extractForms(),
              images: this.extractImages(),
              links: this.extractLinks(),
              headings: this.extractHeadings(),
              inputs: this.extractInputs(),
              buttons: this.extractButtons(),
              multimedia: this.extractMultimedia(),
            };
          },

          /**
           * Extract accessibility information
           */
          extractAccessibilityInfo() {
            return {
              landmarks: this.extractLandmarks(),
              ariaElements: this.extractAriaElements(),
              focusableElements: this.extractFocusableElements(),
            };
          },

          /**
           * Extract structural information
           */
          extractStructuralInfo() {
            return {
              doctype: document.doctype ? document.doctype.name : '',
              hasValidHTML: this.validateHTMLStructure(),
              semanticStructure: this.extractSemanticElements(),
              colorContrast: this.extractColorContrastElements(),
            };
          },

          /**
           * Extract forms with accessibility information
           */
          extractForms() {
            const forms = Array.from(document.querySelectorAll('form'));
            return forms.map((form, index) => {
              const selector = form.id ? `#${form.id}` : `form:nth-child(${index + 1})`;
              const fields = Array.from(form.querySelectorAll('input, select, textarea')).map(field => ({
                selector: field.id ? `#${field.id}` : field.tagName.toLowerCase(),
                type: (field as HTMLInputElement).type || 'text',
                name: (field as HTMLInputElement).name || '',
                id: field.id || '',
                hasLabel: !!form.querySelector(`label[for="${field.id}"]`),
                labelText: form.querySelector(`label[for="${field.id}"]`)?.textContent?.trim() || '',
                isRequired: (field as HTMLInputElement).required,
                accessibility: {
                  hasAccessibleName: !!(field.getAttribute('aria-label') || field.getAttribute('aria-labelledby')),
                  hasDescription: !!field.getAttribute('aria-describedby'),
                  hasValidation: !!(field.getAttribute('aria-invalid') || field.getAttribute('required')),
                  isKeyboardAccessible: field.tabIndex >= 0,
                },
              }));

              return {
                selector,
                tagName: form.tagName,
                action: form.action || '',
                method: form.method || 'get',
                hasLabel: !!form.querySelector('legend, h1, h2, h3, h4, h5, h6'),
                fields,
                accessibility: {
                  hasAccessibleName: !!(form.getAttribute('aria-label') || form.getAttribute('aria-labelledby')),
                  hasErrorHandling: !!form.querySelector('[aria-live], [role="alert"]'),
                  hasValidation: !!form.querySelector('[aria-invalid], [required]'),
                },
              };
            });
          },

          /**
           * Extract images with accessibility information
           */
          extractImages() {
            const images = Array.from(document.querySelectorAll('img'));
            return images.map((img, index) => {
              const selector = img.id ? `#${img.id}` : `img:nth-child(${index + 1})`;
              const alt = img.alt || '';
              const hasAltText = alt.length > 0;
              const isDecorative = alt === '' && (img.getAttribute('role') === 'presentation' || img.getAttribute('role') === 'none');

              return {
                selector,
                src: img.src || '',
                alt,
                hasAltText,
                isDecorative,
                dimensions: { width: img.naturalWidth || 0, height: img.naturalHeight || 0 },
                accessibility: {
                  hasProperAlt: hasAltText && alt.length > 2,
                  hasLongDesc: !!img.getAttribute('longdesc'),
                  hasAriaLabel: !!img.getAttribute('aria-label'),
                },
              };
            });
          },

          /**
           * Extract links with accessibility information
           */
          extractLinks() {
            const links = Array.from(document.querySelectorAll('a[href]'));
            return links.map((link, index) => {
              const selector = link.id ? `#${link.id}` : `a:nth-child(${index + 1})`;
              const text = link.textContent?.trim() || '';
              const href = link.href || '';

              return {
                selector,
                href,
                text,
                hasText: text.length > 0,
                isExternal: href.startsWith('http') && !href.includes(window.location.hostname),
                accessibility: {
                  hasAccessibleName: !!(text || link.getAttribute('aria-label') || link.getAttribute('title')),
                  hasProperContext: text.length > 3 && !['click here', 'read more', 'more'].includes(text.toLowerCase()),
                  isKeyboardAccessible: link.tabIndex >= 0,
                },
              };
            });
          },

          /**
           * Extract headings with hierarchy information
           */
          extractHeadings() {
            const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
            return headings.map((heading, index) => {
              const selector = heading.id ? `#${heading.id}` : `${heading.tagName.toLowerCase()}:nth-child(${index + 1})`;
              const text = heading.textContent?.trim() || '';
              const level = parseInt(heading.tagName.charAt(1));

              return {
                selector,
                tagName: heading.tagName,
                level,
                text,
                hasText: text.length > 0,
                accessibility: {
                  hasProperHierarchy: true, // Will be validated later
                  isUnique: headings.filter(h => h.textContent?.trim() === text).length === 1,
                },
              };
            });
          },

          // Additional extraction methods would continue here...
          // For brevity, I'll add placeholder methods

          extractInputs() {
            return Array.from(document.querySelectorAll('input')).map((input, index) => ({
              selector: input.id ? `#${input.id}` : `input:nth-child(${index + 1})`,
              type: input.type || 'text',
              name: input.name || '',
              id: input.id || '',
              hasLabel: !!document.querySelector(`label[for="${input.id}"]`),
              labelText: document.querySelector(`label[for="${input.id}"]`)?.textContent?.trim() || '',
              isRequired: input.required,
              accessibility: {
                hasAccessibleName: !!(input.getAttribute('aria-label') || input.getAttribute('aria-labelledby')),
                hasDescription: !!input.getAttribute('aria-describedby'),
                hasValidation: !!(input.getAttribute('aria-invalid') || input.required),
                isKeyboardAccessible: input.tabIndex >= 0,
              },
            }));
          },

          extractButtons() {
            return Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]')).map((button, index) => ({
              selector: button.id ? `#${button.id}` : `button:nth-child(${index + 1})`,
              type: (button as HTMLButtonElement).type || 'button',
              text: button.textContent?.trim() || (button as HTMLInputElement).value || '',
              hasText: (button.textContent?.trim() || (button as HTMLInputElement).value || '').length > 0,
              accessibility: {
                hasAccessibleName: !!(button.textContent?.trim() || button.getAttribute('aria-label')),
                isKeyboardAccessible: button.tabIndex >= 0,
                hasProperRole: button.getAttribute('role') !== 'presentation',
              },
            }));
          },

          extractMultimedia() {
            const multimedia = Array.from(document.querySelectorAll('video, audio, iframe'));
            return multimedia.map((element, index) => ({
              selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
              type: element.tagName.toLowerCase() as 'video' | 'audio' | 'iframe',
              src: (element as HTMLVideoElement).src || (element as HTMLIFrameElement).src || '',
              accessibility: {
                hasCaptions: !!element.querySelector('track[kind="captions"]'),
                hasTranscript: !!document.querySelector('[data-transcript]'),
                hasAudioDescription: !!element.querySelector('track[kind="descriptions"]'),
                hasAccessibleControls: (element as HTMLVideoElement).controls !== undefined,
              },
            }));
          },

          extractLandmarks() {
            const landmarks = Array.from(document.querySelectorAll('[role], main, nav, header, footer, aside, section'));
            return landmarks.map((element, index) => ({
              selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
              role: element.getAttribute('role') || element.tagName.toLowerCase(),
              hasLabel: !!(element.getAttribute('aria-label') || element.getAttribute('aria-labelledby')),
              labelText: element.getAttribute('aria-label') || '',
            }));
          },

          extractAriaElements() {
            const ariaElements = Array.from(document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]'));
            return ariaElements.map((element, index) => {
              const ariaAttributes: Record<string, string> = {};
              for (const attr of element.attributes) {
                if (attr.name.startsWith('aria-') || attr.name === 'role') {
                  ariaAttributes[attr.name] = attr.value;
                }
              }

              return {
                selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
                ariaAttributes,
                hasValidAria: Object.keys(ariaAttributes).length > 0,
              };
            });
          },

          extractFocusableElements() {
            const focusable = Array.from(document.querySelectorAll('a, button, input, select, textarea, [tabindex]'));
            return focusable.map((element, index) => ({
              selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
              tabIndex: element.tabIndex,
              isKeyboardAccessible: element.tabIndex >= 0,
              hasFocusIndicator: true, // Will be validated with focus styles
            }));
          },

          validateHTMLStructure() {
            return !!(document.doctype && document.documentElement.lang);
          },

          extractSemanticElements() {
            const semantic = Array.from(document.querySelectorAll('main, nav, header, footer, aside, section, article'));
            return semantic.map((element, index) => ({
              selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
              tagName: element.tagName,
              semanticMeaning: element.tagName.toLowerCase(),
              hasProperStructure: true,
            }));
          },

          extractColorContrastElements() {
            const textElements = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, a, button, label'));
            return {
              textElements: textElements.slice(0, 50).map((element, index) => { // Limit to 50 for performance
                const styles = window.getComputedStyle(element);
                return {
                  selector: element.id ? `#${element.id}` : `${element.tagName.toLowerCase()}:nth-child(${index + 1})`,
                  foreground: styles.color,
                  background: styles.backgroundColor,
                  ratio: 0, // Will be calculated later
                };
              }),
            };
          },
        };

        console.log('✅ Unified DOM extraction functions injected successfully');
      });

      // Wait for injection to complete
      await page.waitForFunction(
        () => typeof (window as any).unifiedExtractor !== 'undefined',
        { timeout: 5000 }
      );

      logger.debug('✅ DOM extraction functions validated successfully');
    } catch (error) {
      logger.error('❌ DOM extraction injection failed:', error);
      throw new Error(`DOM extraction injection failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

export default UnifiedDOMExtractor;
