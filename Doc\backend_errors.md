[ENV_DEBUG] dbHost chosen: localhost
[ENV_DEBUG] Constructed DATABASE_URL: postgresql://complyuser:complypassword@localhost:5432/complychecker_dev
2025-07-11T14:38:19.513Z [INFO] - 🗄️ Smart cache initialized - {"maxSize":100,"maxEntries":500,"def aultTTL":1800000,"defaultTTLMinutes":30}
2025-07-11T14:38:22.207Z [DEBUG] - Readability analysis library not available
2025-07-11T14:38:22.234Z [DEBUG] - Language detection library not available
D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:859
    return new TSError(diagnosticText, diagnosticCodes, diagnostics);
           ^
TSError: ⨯ Unable to compile TypeScript:
src/compliance/wcag/utils/form-accessibility-analyzer.ts:160:30 - error TS2339: Property 'getForms' does not exist on type 'FormAccessibilityAnalyzer'.

160     const forms = await this.getForms(page, fullConfig);
                                 ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:166:37 - error TS2339: Property 'analyzeForm' does not exist on type 'FormAccessibilityAnalyzer'.

166         const analysis = await this.analyzeForm(page, form, fullConfig);
                                        ~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:174:25 - error TS2339: Property 'generateReport' does not exist on type 'FormAccessibilityAnalyzer'.

174     const report = this.generateReport(formAnalyses);
                            ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:9 - error TS2304: Cannot find name 'isVisible'.

276         isVisible(element: HTMLElement): boolean {
            ~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:19 - error TS2304: Cannot find name 'element'.

276         isVisible(element: HTMLElement): boolean {
                      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:42 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

276         isVisible(element: HTMLElement): boolean {
                                             ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:277:49 - error TS2552: Cannot find name 'element'. Did you mean 'Element'?

277           const style = window.getComputedStyle(element);
                                                    ~~~~~~~

  ../node_modules/typescript/lib/lib.dom.d.ts:8491:13
    8491 declare var Element: {
                     ~~~~~~~
    'Element' is declared here.
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:9 - error TS2304: Cannot find name 'analyzeFieldLabel'.

284         analyzeFieldLabel(field: HTMLElement): {
            ~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:27 - error TS2304: Cannot find name 'field'.

284         analyzeFieldLabel(field: HTMLElement): {
                              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:285:21 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

285           hasLabel: boolean;
                        ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:286:22 - error TS2693: 'string' only refers to a type, but is being used as a value here.

286           labelText: string;
                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:287:24 - error TS2693: 'string' only refers to a type, but is being used as a value here.

287           labelMethod: string;
                           ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:288:25 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

288           isAccessible: boolean;
                            ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:289:19 - error TS2693: 'string' only refers to a type, but is being used as a value here.

289           issues: string[];
                      ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:297:15 - error TS2304: Cannot find name 'field'.

297           if (field.id) {
                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:298:72 - error TS2304: Cannot find name 'field'.

298             const explicitLabel = document.querySelector(`label[for="${field.id}"]`);
                                                                           ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:300:27 - error TS18047: 'explicitLabel' is possibly 'null'.

300               labelText = explicitLabel.textContent?.trim() || '';
                              ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:308:35 - error TS2304: Cannot find name 'field'.

308             const implicitLabel = field.closest('label');
                                      ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:318:31 - error TS2304: Cannot find name 'field'.

318             const ariaLabel = field.getAttribute('aria-label');
                                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:328:36 - error TS2304: Cannot find name 'field'.

328             const ariaLabelledby = field.getAttribute('aria-labelledby');
                                       ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:332:29 - error TS18047: 'labelElement' is possibly 'null'.

332                 labelText = labelElement.textContent?.trim() || '';
                                ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:341:33 - error TS2304: Cannot find name 'field'.

341             const placeholder = field.getAttribute('placeholder');
                                    ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:366:9 - error TS2304: Cannot find name 'analyzeFieldValidation'.

366         analyzeFieldValidation(field: HTMLElement): {
            ~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:366:32 - error TS2304: Cannot find name 'field'.

366         analyzeFieldValidation(field: HTMLElement): {
                                   ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:367:23 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

367           isRequired: boolean;
                          ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:368:26 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

368           hasValidation: boolean;
                             ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:369:29 - error TS2693: 'string' only refers to a type, but is being used as a value here.

369           validationMethod: string[];
                                ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:370:26 - error TS2693: 'string' only refers to a type, but is being used as a value here.

370           errorMessages: string[];
                             ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:371:32 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

371           hasAccessibleErrors: boolean;
                                   ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:372:19 - error TS2693: 'string' only refers to a type, but is being used as a value here.

372           issues: string[];
                      ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:379:13 - error TS2304: Cannot find name 'field'.

379             field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';
                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:379:47 - error TS2304: Cannot find name 'field'.

379             field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';
                                                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:386:32 - error TS2304: Cannot find name 'field'.

386           const inputElement = field as HTMLInputElement;
                                   ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:398:15 - error TS2304: Cannot find name 'field'.

398           if (field.getAttribute('aria-invalid')) {
                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:403:35 - error TS2304: Cannot find name 'field'.

403           const ariaDescribedby = field.getAttribute('aria-describedby');
                                      ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:407:33 - error TS18047: 'errorElement' is possibly 'null'.

407               const errorText = errorElement.textContent?.trim();
                                    ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:409:36 - error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.

409                 errorMessages.push(errorText);
                                       ~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:416:13 - error TS2304: Cannot find name 'field'.

416             field.closest('.field, .form-group, .input-group') || field.parentElement;
                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:416:67 - error TS2304: Cannot find name 'field'.

416             field.closest('.field, .form-group, .input-group') || field.parentElement;
                                                                      ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:421:36 - error TS7006: Parameter 'errorEl' implicitly has an 'any' type.

421             errorElements.forEach((errorEl) => {
                                       ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:432:53 - error TS2304: Cannot find name 'field'.

432           if (isRequired && !hasAccessibleErrors && field.getAttribute('aria-invalid') === 'true') {
                                                        ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:449:9 - error TS2304: Cannot find name 'analyzeFieldGrouping'.

449         analyzeFieldGrouping(field: HTMLElement): {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:449:30 - error TS2304: Cannot find name 'field'.

449         analyzeFieldGrouping(field: HTMLElement): {
                                 ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:450:25 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

450           isInFieldset: boolean;
                            ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:451:11 - error TS2304: Cannot find name 'fieldsetLegend'.

451           fieldsetLegend?: string;
              ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:451:28 - error TS2693: 'string' only refers to a type, but is being used as a value here.

451           fieldsetLegend?: string;
                               ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:452:22 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

452           isInGroup: boolean;
                         ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:453:11 - error TS2304: Cannot find name 'groupLabel'.

453           groupLabel?: string;
              ~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:453:24 - error TS2693: 'string' only refers to a type, but is being used as a value here.

453           groupLabel?: string;
                           ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:455:28 - error TS2304: Cannot find name 'field'.

455           const fieldset = field.closest('fieldset');
                               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:465:25 - error TS2304: Cannot find name 'field'.

465           const group = field.closest('[role="group"]');
                            ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:488:9 - error TS2304: Cannot find name 'analyzeAutocomplete'.

488         analyzeAutocomplete(field: HTMLElement): {
            ~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:488:29 - error TS2304: Cannot find name 'field'.

488         analyzeAutocomplete(field: HTMLElement): {
                                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:489:28 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

489           hasAutocomplete: boolean;
                               ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:490:11 - error TS2304: Cannot find name 'autocompleteValue'.

490           autocompleteValue?: string;
              ~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:490:31 - error TS2693: 'string' only refers to a type, but is being used as a value here.

490           autocompleteValue?: string;
                                  ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:491:32 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

491           isValidAutocomplete: boolean;
                                   ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:493:37 - error TS2304: Cannot find name 'field'.

493           const autocompleteValue = field.getAttribute('autocomplete');
                                        ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:532:9 - error TS2304: Cannot find name 'analyzeAccessibility'.

532         analyzeAccessibility(field: HTMLElement): {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:532:30 - error TS2304: Cannot find name 'field'.

532         analyzeAccessibility(field: HTMLElement): {
                                 ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:533:33 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

533           isKeyboardAccessible: boolean;
                                    ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:534:27 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

534           hasProperFocus: boolean;
                              ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:535:27 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

535           hasDescription: boolean;
                              ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:536:27 - error TS2693: 'string' only refers to a type, but is being used as a value here.

536           ariaAttributes: string[];
                              ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:537:19 - error TS2693: 'string' only refers to a type, but is being used as a value here.

537           issues: string[];
                      ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:544:13 - error TS2304: Cannot find name 'field'.

544             field.tabIndex >= 0 ||
                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:545:64 - error TS2304: Cannot find name 'field'.

545             ['INPUT', 'SELECT', 'TEXTAREA', 'BUTTON'].includes(field.tagName);
                                                                   ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:548:11 - error TS2304: Cannot find name 'field'.

548           field.focus();
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:549:49 - error TS2304: Cannot find name 'field'.

549           const style = window.getComputedStyle(field, ':focus');
                                                    ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:558:13 - error TS2304: Cannot find name 'field'.

558             field.getAttribute('aria-describedby') || field.getAttribute('title')
                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:558:55 - error TS2304: Cannot find name 'field'.

558             field.getAttribute('aria-describedby') || field.getAttribute('title')
                                                          ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:562:22 - error TS2304: Cannot find name 'field'.

562           Array.from(field.attributes).forEach((attr) => {
                         ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:563:17 - error TS18046: 'attr' is of type 'unknown'.

563             if (attr.name.startsWith('aria-')) {
                    ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:564:35 - error TS18046: 'attr' is of type 'unknown'.

564               ariaAttributes.push(attr.name);
                                      ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:9 - error TS2304: Cannot find name 'getFormFields'.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
            ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:23 - error TS2304: Cannot find name 'form'.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                          ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:46 - error TS2304: Cannot find name 'includeHidden'.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                 ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:61 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                                ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:71 - error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ new (): HTMLElement; prototype: HTMLElement; }'.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                                          ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:597:15 - error TS2304: Cannot find name 'includeHidden'.

597           if (includeHidden) {
                  ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:603:30 - error TS2304: Cannot find name 'form'.

603             const elements = form.querySelectorAll(selector);
                                 ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:604:31 - error TS7006: Parameter 'el' implicitly has an 'any' type.

604             elements.forEach((el) => {
                                  ~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:606:19 - error TS2304: Cannot find name 'includeHidden'.

606               if (includeHidden || formAnalysis.isVisible(element)) {
                      ~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:618:9 - error TS2304: Cannot find name 'analyzeErrorHandling'.

618         analyzeErrorHandling(form: HTMLFormElement): {
            ~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:618:30 - error TS2304: Cannot find name 'form'.

618         analyzeErrorHandling(form: HTMLFormElement): {
                                 ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:619:28 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

619           hasErrorSummary: boolean;
                               ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:620:26 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

620           hasLiveRegion: boolean;
                             ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:621:33 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

621           errorsLinkedToFields: boolean;
                                    ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:624:32 - error TS2304: Cannot find name 'form'.

624           const errorSummary = form.querySelector('.error-summary, [role="alert"], .alert-error');
                                   ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:628:30 - error TS2304: Cannot find name 'form'.

628           const liveRegion = form.querySelector('[aria-live]');
                                 ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:632:33 - error TS2304: Cannot find name 'form'.

632           const errorElements = form.querySelectorAll('.error, .invalid, [role="alert"]');
                                    ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:635:34 - error TS7006: Parameter 'errorEl' implicitly has an 'any' type.

635           errorElements.forEach((errorEl) => {
                                     ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:640:35 - error TS2304: Cannot find name 'form'.

640               const linkedField = form.querySelector(`[aria-describedby*="${errorId}"]`);
                                      ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:659:9 - error TS2304: Cannot find name 'analyzeProgressIndicator'.

659         analyzeProgressIndicator(form: HTMLFormElement): {
            ~~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:659:34 - error TS2304: Cannot find name 'form'.

659         analyzeProgressIndicator(form: HTMLFormElement): {
                                     ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:660:24 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

660           hasProgress: boolean;
                           ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:661:25 - error TS2693: 'boolean' only refers to a type, but is being used as a value here.

661           isAccessible: boolean;
                            ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:663:36 - error TS2304: Cannot find name 'form'.

663           const progressElements = form.querySelectorAll(
                                       ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:670:39 - error TS7006: Parameter 'progress' implicitly has an 'any' type.

670             progressElements.forEach((progress) => {
                                          ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:717:12 - error TS7006: Parameter 'injectionError' implicitly has an 'any' type.

717   } catch (injectionError) {
               ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:11 - error TS2304: Cannot find name 'async'.

726   private async getForms(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:17 - error TS2304: Cannot find name 'getForms'.

726   private async getForms(
                    ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:727:5 - error TS2304: Cannot find name 'page'.

727     page: Page,
        ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:728:5 - error TS2304: Cannot find name '_config'.

728     _config: FormAnalysisConfig,
        ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:728:14 - error TS2693: 'FormAnalysisConfig' only refers to a type, but is being used as a value here.

728     _config: FormAnalysisConfig,
                 ~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:6 - error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ return: any; }'.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
730     return await page.evaluate(() => {
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
...
752     });
    ~~~~~~~
753   }
    ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:14 - error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ selector: any; element: { tagName: any; action: any; method: any; }; }'.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:26 - error TS2693: 'string' only refers to a type, but is being used as a value here.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                             ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:54 - error TS2693: 'string' only refers to a type, but is being used as a value here.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:70 - error TS2693: 'string' only refers to a type, but is being used as a value here.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:86 - error TS2693: 'string' only refers to a type, but is being used as a value here.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                                         ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:730:18 - error TS2304: Cannot find name 'page'.

730     return await page.evaluate(() => {
                     ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:758:11 - error TS2304: Cannot find name 'async'.

758   private async analyzeForm(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:758:17 - error TS2304: Cannot find name 'analyzeForm'.

758   private async analyzeForm(
                    ~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:759:5 - error TS2304: Cannot find name 'page'.

759     page: Page,
        ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:5 - error TS2304: Cannot find name 'formInfo'.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
        ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:27 - error TS2693: 'string' only refers to a type, but is being used as a value here.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                              ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:55 - error TS2693: 'string' only refers to a type, but is being used as a value here.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:71 - error TS2693: 'string' only refers to a type, but is being used as a value here.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:87 - error TS2693: 'string' only refers to a type, but is being used as a value here.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                                          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:761:5 - error TS2304: Cannot find name 'config'.

761     config: FormAnalysisConfig,
        ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:761:13 - error TS2693: 'FormAnalysisConfig' only refers to a type, but is being used as a value here.

761     config: FormAnalysisConfig,
                ~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:762:6 - error TS2365: Operator '>' cannot be applied to types 'boolean' and '{ return: any; }'.

762   ): Promise<FormAnalysis> {
         ~~~~~~~~~~~~~~~~~~~~~~~
763     return await page.evaluate(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
...
997     );
    ~~~~~~
998   }
    ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:762:14 - error TS2693: 'FormAnalysis' only refers to a type, but is being used as a value here.

762   ): Promise<FormAnalysis> {
                 ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:763:18 - error TS2304: Cannot find name 'page'.

763     return await page.evaluate(
                     ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:764:8 - error TS7006: Parameter 'selector' implicitly has an 'any' type.

764       (selector, analysisConfig) => {
           ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:764:18 - error TS7006: Parameter 'analysisConfig' implicitly has an 'any' type.

764       (selector, analysisConfig) => {
                     ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:995:7 - error TS2304: Cannot find name 'formInfo'.

995       formInfo.selector,
          ~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:996:7 - error TS2304: Cannot find name 'config'.

996       config,
          ~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:11 - error TS2304: Cannot find name 'generateReport'.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
               ~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:26 - error TS2304: Cannot find name 'forms'.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:33 - error TS2693: 'FormAnalysis' only refers to a type, but is being used as a value here.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                     ~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:50 - error TS2693: 'FormAccessibilityReport' only refers to a type, but is being used as a value here.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                      ~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1004:24 - error TS2304: Cannot find name 'forms'.

1004     const totalForms = forms.length;
                            ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1005:25 - error TS2304: Cannot find name 'forms'.

1005     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                             ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1005:39 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

1005     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                                           ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1005:44 - error TS7006: Parameter 'form' implicitly has an 'any' type.

1005     const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
                                                ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1006:29 - error TS2304: Cannot find name 'forms'.

1006     const accessibleForms = forms.filter((form) => form.overallScore >= 80).length;
                                 ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1006:43 - error TS7006: Parameter 'form' implicitly has an 'any' type.

1006     const accessibleForms = forms.filter((form) => form.overallScore >= 80).length;
                                               ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1007:30 - error TS2304: Cannot find name 'forms'.

1007     const accessibleFields = forms.reduce(
                                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1008:8 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

1008       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
            ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1008:13 - error TS7006: Parameter 'form' implicitly has an 'any' type.

1008       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
                 ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1008:48 - error TS7006: Parameter 'field' implicitly has an 'any' type.

1008       (sum, form) => sum + form.fields.filter((field) => field.score >= 80).length,
                                                    ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1013:24 - error TS2304: Cannot find name 'forms'.

1013     const formScores = forms.map((form) => form.overallScore);
                            ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1013:35 - error TS7006: Parameter 'form' implicitly has an 'any' type.

1013     const formScores = forms.map((form) => form.overallScore);
                                       ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1016:41 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

1016         ? Math.round(formScores.reduce((sum, score) => sum + score, 0) / formScores.length)
                                             ~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1016:46 - error TS7006: Parameter 'score' implicitly has an 'any' type.

1016         ? Math.round(formScores.reduce((sum, score) => sum + score, 0) / formScores.length)
                                                  ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1021:5 - error TS2304: Cannot find name 'forms'.

1021     forms.forEach((form) => {
         ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1021:20 - error TS7006: Parameter 'form' implicitly has an 'any' type.

1021     forms.forEach((form) => {
                        ~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1023:28 - error TS7006: Parameter 'field' implicitly has an 'any' type.

1023       form.fields.forEach((field) => {
                                ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:26 - error TS1005: ',' expected.

276         isVisible(element: HTMLElement): boolean {
                             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:40 - error TS1005: ';' expected.

276         isVisible(element: HTMLElement): boolean {
                                           ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:276:42 - error TS1434: Unexpected keyword or identifier.

276         isVisible(element: HTMLElement): boolean {
                                             ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:279:10 - error TS1128: Declaration or statement expected.

279         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:32 - error TS1005: ',' expected.

284         analyzeFieldLabel(field: HTMLElement): {
                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:284:46 - error TS1005: ';' expected.

284         analyzeFieldLabel(field: HTMLElement): {
                                                 ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:289:26 - error TS1011: An element access expression should take an argument.

289           issues: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:361:10 - error TS1128: Declaration or statement expected.

361         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:366:37 - error TS1005: ',' expected.

366         analyzeFieldValidation(field: HTMLElement): {
                                        ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:366:51 - error TS1005: ';' expected.

366         analyzeFieldValidation(field: HTMLElement): {
                                                      ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:369:36 - error TS1011: An element access expression should take an argument.

369           validationMethod: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:370:33 - error TS1011: An element access expression should take an argument.

370           errorMessages: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:372:26 - error TS1011: An element access expression should take an argument.

372           issues: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:444:10 - error TS1128: Declaration or statement expected.

444         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:449:35 - error TS1005: ',' expected.

449         analyzeFieldGrouping(field: HTMLElement): {
                                      ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:449:49 - error TS1005: ';' expected.

449         analyzeFieldGrouping(field: HTMLElement): {
                                                    ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:451:26 - error TS1109: Expression expected.

451           fieldsetLegend?: string;
                             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:453:22 - error TS1109: Expression expected.

453           groupLabel?: string;
                         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:483:10 - error TS1128: Declaration or statement expected.

483         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:488:34 - error TS1005: ',' expected.

488         analyzeAutocomplete(field: HTMLElement): {
                                     ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:488:48 - error TS1005: ';' expected.

488         analyzeAutocomplete(field: HTMLElement): {
                                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:490:29 - error TS1109: Expression expected.

490           autocompleteValue?: string;
                                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:527:10 - error TS1128: Declaration or statement expected.

527         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:532:35 - error TS1005: ',' expected.

532         analyzeAccessibility(field: HTMLElement): {
                                      ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:532:49 - error TS1005: ';' expected.

532         analyzeAccessibility(field: HTMLElement): {
                                                    ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:536:34 - error TS1011: An element access expression should take an argument.

536           ariaAttributes: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:537:26 - error TS1011: An element access expression should take an argument.

537           issues: string[];

src/compliance/wcag/utils/form-accessibility-analyzer.ts:575:10 - error TS1128: Declaration or statement expected.

575         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:27 - error TS1005: ',' expected.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                              ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:59 - error TS1005: ',' expected.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                              ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:69 - error TS1005: ';' expected.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                                        ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:83 - error TS1011: An element access expression should take an argument.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:580:85 - error TS1005: ';' expected.

580         getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
                                                                                        ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:613:10 - error TS1128: Declaration or statement expected.

613         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:618:34 - error TS1005: ',' expected.

618         analyzeErrorHandling(form: HTMLFormElement): {
                                     ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:618:52 - error TS1005: ';' expected.

618         analyzeErrorHandling(form: HTMLFormElement): {
                                                       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:654:10 - error TS1128: Declaration or statement expected.

654         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:659:38 - error TS1005: ',' expected.

659         analyzeProgressIndicator(form: HTMLFormElement): {
                                         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:659:56 - error TS1005: ';' expected.

659         analyzeProgressIndicator(form: HTMLFormElement): {
                                                           ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:691:10 - error TS1128: Declaration or statement expected.

691         },
             ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:692:8 - error TS1005: ')' expected.

692       };
           ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:700:6 - error TS1472: 'catch' or 'finally' expected.

700     });
         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:3 - error TS1128: Declaration or statement expected.

726   private async getForms(
      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:726:11 - error TS1434: Unexpected keyword or identifier.

726   private async getForms(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:727:9 - error TS1005: ',' expected.

727     page: Page,
            ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:728:12 - error TS1005: ',' expected.

728     _config: FormAnalysisConfig,
               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:4 - error TS1005: ';' expected.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:32 - error TS1005: ',' expected.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:60 - error TS1005: ',' expected.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:76 - error TS1005: ',' expected.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {
                                                                               ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:729:97 - error TS1011: An element access expression should take an argument.

729   ): Promise<{ selector: string; element: { tagName: string; action: string; method: string } }[]> {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:730:12 - error TS1005: ':' expected.

730     return await page.evaluate(() => {
               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:752:7 - error TS1005: ',' expected.

752     });
          ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:758:3 - error TS1128: Declaration or statement expected.

758   private async analyzeForm(
      ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:758:11 - error TS1434: Unexpected keyword or identifier.

758   private async analyzeForm(
              ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:759:9 - error TS1005: ',' expected.

759     page: Page,
            ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:13 - error TS1005: ',' expected.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:33 - error TS1005: ',' expected.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                    ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:61 - error TS1005: ',' expected.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:760:77 - error TS1005: ',' expected.

760     formInfo: { selector: string; element: { tagName: string; action: string; method: string } },
                                                                                ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:761:11 - error TS1005: ',' expected.

761     config: FormAnalysisConfig,
              ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:762:4 - error TS1005: ';' expected.

762   ): Promise<FormAnalysis> {
       ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:763:12 - error TS1005: ':' expected.

763     return await page.evaluate(
               ~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:997:6 - error TS1005: ',' expected.

997     );
         ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:3 - error TS1128: Declaration or statement expected.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
       ~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:31 - error TS1005: ',' expected.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                   ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:46 - error TS1011: An element access expression should take an argument.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {

src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:48 - error TS1005: ';' expected.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                    ~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1003:50 - error TS1434: Unexpected keyword or identifier.

1003   private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
                                                      ~~~~~~~~~~~~~~~~~~~~~~~
src/compliance/wcag/utils/form-accessibility-analyzer.ts:1078:1 - error TS1128: Declaration or statement expected.

1078 }
     ~

    at createTSError (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:859:12)
    at reportTSError (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:863:19)
    at getOutput (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1077:36)
    at Object.compile (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1433:41)
    at Module.m._compile (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1617:30)
    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)
    at Object.require.extensions.<computed> [as .ts] (D:\Web projects\Comply Checker\node_modules\ts-node\src\index.ts:1621:12)
    at Module.load (node:internal/modules/cjs/loader:1288:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1104:12)
    at Module.require (node:internal/modules/cjs/loader:1311:19) {
  diagnosticCodes: [
     2339,  2339,  2339, 2304, 2304, 2693, 2552,  2304,  2304,
     2693,  2693,  2693, 2693, 2693, 2304, 2304, 18047,  2304,
     2304,  2304, 18047, 2304, 2304, 2304, 2693,  2693,  2693,
     2693,  2693,  2693, 2304, 2304, 2304, 2304,  2304, 18047,
     2345,  2304,  2304, 7006, 2304, 2304, 2304,  2693,  2304,
     2693,  2693,  2304, 2693, 2304, 2304, 2304,  2304,  2693,
     2304,  2693,  2693, 2304, 2304, 2304, 2693,  2693,  2693,
     2693,  2693,  2304, 2304, 2304, 2304, 2304,  2304,  2304,
    18046, 18046,  2304, 2304, 2304, 2693, 7053,  2304,  2304,
     7006,  2304,  2304, 2304, 2693, 2693, 2693,  2304,  2304,
     2304,  7006,  2304, 2304, 2304, 2693, 2693,  2304,  7006,
     7006,
    ... 120 more items
  ]
}
[nodemon] app crashed - waiting for file changes before starting...
